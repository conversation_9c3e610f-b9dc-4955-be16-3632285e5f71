import { Switch, Route, useLocation } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import Home from "@/pages/home";
import NotFound from "@/pages/not-found";
import PrivacyPolicy from "@/pages/privacy-policy";
import TermsOfService from "@/pages/terms-of-service";
import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { supportedLanguages } from "./lib/i18n";
import "./lib/i18n";

function Router() {
  const [location] = useLocation();
  const { i18n } = useTranslation();

  // 语言检测和设置
  useEffect(() => {
    const currentPath = location;
    const langMatch = currentPath.match(/^\/([a-z]{2})\//);
    let detectedLang = 'en';

    if (langMatch) {
      const pathLang = langMatch[1];
      if (supportedLanguages.some(lang => lang.code === pathLang)) {
        detectedLang = pathLang;
      }
    }

    // 更新i18n语言设置
    if (detectedLang !== i18n.language) {
      i18n.changeLanguage(detectedLang);
    }
  }, [location, i18n]);

  return (
    <Switch>
      {/* 英文路由（默认，无前缀） */}
      <Route path="/" component={Home} />
      <Route path="/note/:shareId" component={Home} />
      <Route path="/privacy-policy" component={PrivacyPolicy} />
      <Route path="/terms-of-service" component={TermsOfService} />

      {/* 非英文语言路由 */}
      <Route path="/zh/" component={Home} />
      <Route path="/zh/note/:shareId" component={Home} />
      <Route path="/zh/privacy-policy" component={PrivacyPolicy} />
      <Route path="/zh/terms-of-service" component={TermsOfService} />

      <Route path="/es/" component={Home} />
      <Route path="/es/note/:shareId" component={Home} />
      <Route path="/es/privacy-policy" component={PrivacyPolicy} />
      <Route path="/es/terms-of-service" component={TermsOfService} />

      <Route path="/fr/" component={Home} />
      <Route path="/fr/note/:shareId" component={Home} />
      <Route path="/fr/privacy-policy" component={PrivacyPolicy} />
      <Route path="/fr/terms-of-service" component={TermsOfService} />

      <Route path="/de/" component={Home} />
      <Route path="/de/note/:shareId" component={Home} />
      <Route path="/de/privacy-policy" component={PrivacyPolicy} />
      <Route path="/de/terms-of-service" component={TermsOfService} />

      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  useEffect(() => {
    // Set up global keyboard shortcuts
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 's':
            e.preventDefault();
            // Save functionality will be handled by the editor
            break;
          case 'f':
            e.preventDefault();
            // Find & Replace will be handled by the editor
            break;
          case 'p':
            e.preventDefault();
            // Print functionality will be handled by the editor
            break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Router />
      </TooltipProvider>
    </QueryClientProvider>
  );
}

export default App;
